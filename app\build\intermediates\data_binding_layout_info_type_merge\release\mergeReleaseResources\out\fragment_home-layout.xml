<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="com.example.blinksky" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_home_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="392" endOffset="12"/></Target><Target id="@+id/location_text" view="TextView"><Expressions/><location startLine="36" startOffset="16" endLine="42" endOffset="45"/></Target><Target id="@+id/weather_card" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="56" startOffset="8" endLine="149" endOffset="43"/></Target><Target id="@+id/temperature" view="TextView"><Expressions/><location startLine="78" startOffset="20" endLine="86" endOffset="50"/></Target><Target id="@+id/weather_icon" view="ImageView"><Expressions/><location startLine="88" startOffset="20" endLine="94" endOffset="58"/></Target><Target id="@+id/weather_details" view="TextView"><Expressions/><location startLine="98" startOffset="16" endLine="105" endOffset="45"/></Target><Target id="@+id/weather_detail_button" view="Button"><Expressions/><location startLine="140" startOffset="16" endLine="147" endOffset="45"/></Target><Target id="@+id/music_card" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="152" startOffset="8" endLine="246" endOffset="43"/></Target><Target id="@+id/outfit_card" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="249" startOffset="8" endLine="389" endOffset="43"/></Target></Targets></Layout>