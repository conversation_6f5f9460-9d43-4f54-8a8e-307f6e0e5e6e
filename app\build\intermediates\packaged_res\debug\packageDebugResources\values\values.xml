<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="gray_background">#FF9E9E9E</color>
    <color name="gray_card">#FF757575</color>
    <color name="gray_dark">#FF616161</color>
    <color name="gray_light">#FFE0E0E0</color>
    <color name="pink_light">#FFF8BBD9</color>
    <color name="pink_primary">#FFE91E63</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_gray">#FFBDBDBD</color>
    <color name="text_white">#FFFFFFFF</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <string name="app_name">BLINKSKY</string>
    <string name="title_chat">聊天</string>
    <string name="title_dashboard">日记</string>
    <string name="title_home">首页</string>
    <string name="title_notifications">娱乐</string>
    <style name="Theme.BLINKSKY" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/pink_primary</item>
        <item name="colorPrimaryVariant">@color/gray_background</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/pink_primary</item>
        <item name="colorSecondaryVariant">@color/pink_light</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:statusBarColor">@color/gray_background</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <item name="colorSurface">@color/gray_card</item>
        
    </style>
</resources>