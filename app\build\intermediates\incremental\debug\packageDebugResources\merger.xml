<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\BLINKSKY\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\BLINKSKY\app\src\main\res"><file name="bottom_nav_color" path="D:\BLINKSKY\app\src\main\res\color\bottom_nav_color.xml" qualifiers="" type="color"/><file name="circle_pink_background" path="D:\BLINKSKY\app\src\main\res\drawable\circle_pink_background.xml" qualifiers="" type="drawable"/><file name="ic_album_cover" path="D:\BLINKSKY\app\src\main\res\drawable\ic_album_cover.xml" qualifiers="" type="drawable"/><file name="ic_chat" path="D:\BLINKSKY\app\src\main\res\drawable\ic_chat.xml" qualifiers="" type="drawable"/><file name="ic_dashboard_black_24dp" path="D:\BLINKSKY\app\src\main\res\drawable\ic_dashboard_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_heart" path="D:\BLINKSKY\app\src\main\res\drawable\ic_heart.xml" qualifiers="" type="drawable"/><file name="ic_home_black_24dp" path="D:\BLINKSKY\app\src\main\res\drawable\ic_home_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\BLINKSKY\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\BLINKSKY\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_location" path="D:\BLINKSKY\app\src\main\res\drawable\ic_location.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="D:\BLINKSKY\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_notifications_black_24dp" path="D:\BLINKSKY\app\src\main\res\drawable\ic_notifications_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_sunny" path="D:\BLINKSKY\app\src\main\res\drawable\ic_sunny.xml" qualifiers="" type="drawable"/><file name="outfit_placeholder" path="D:\BLINKSKY\app\src\main\res\drawable\outfit_placeholder.xml" qualifiers="" type="drawable"/><file name="pink_button_background" path="D:\BLINKSKY\app\src\main\res\drawable\pink_button_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\BLINKSKY\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_chat" path="D:\BLINKSKY\app\src\main\res\layout\fragment_chat.xml" qualifiers="" type="layout"/><file name="fragment_dashboard" path="D:\BLINKSKY\app\src\main\res\layout\fragment_dashboard.xml" qualifiers="" type="layout"/><file name="fragment_home" path="D:\BLINKSKY\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_notifications" path="D:\BLINKSKY\app\src\main\res\layout\fragment_notifications.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="D:\BLINKSKY\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\BLINKSKY\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\BLINKSKY\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\BLINKSKY\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\BLINKSKY\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\BLINKSKY\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\BLINKSKY\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\BLINKSKY\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\BLINKSKY\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\BLINKSKY\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\BLINKSKY\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\BLINKSKY\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\BLINKSKY\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="mobile_navigation" path="D:\BLINKSKY\app\src\main\res\navigation\mobile_navigation.xml" qualifiers="" type="navigation"/><file path="D:\BLINKSKY\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="pink_primary">#FFE91E63</color><color name="pink_light">#FFF8BBD9</color><color name="gray_background">#FF9E9E9E</color><color name="gray_dark">#FF616161</color><color name="gray_card">#FF757575</color><color name="gray_light">#FFE0E0E0</color><color name="text_white">#FFFFFFFF</color><color name="text_gray">#FFBDBDBD</color></file><file path="D:\BLINKSKY\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen></file><file path="D:\BLINKSKY\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">BLINKSKY</string><string name="title_home">首页</string><string name="title_dashboard">日记</string><string name="title_notifications">娱乐</string><string name="title_chat">聊天</string></file><file path="D:\BLINKSKY\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.BLINKSKY" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/pink_primary</item>
        <item name="colorPrimaryVariant">@color/gray_background</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/pink_primary</item>
        <item name="colorSecondaryVariant">@color/pink_light</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:statusBarColor">@color/gray_background</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <item name="colorSurface">@color/gray_card</item>
        
    </style></file><file path="D:\BLINKSKY\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.BLINKSKY" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="backup_rules" path="D:\BLINKSKY\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\BLINKSKY\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\BLINKSKY\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\BLINKSKY\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\BLINKSKY\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\BLINKSKY\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>