<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.6.0" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.blinksky.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.teal_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="248"
            endLine="7"
            endColumn="27"
            endOffset="263"/>
        <location id="R.color.purple_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="109"
            endLine="4"
            endColumn="29"
            endOffset="126"/>
        <entry
            name="model"
            string="attr[actionBarSize(R),colorPrimaryVariant(R)],color[bottom_nav_color(U),pink_primary(U),text_gray(U),pink_light(U),text_white(U),gray_light(U),gray_dark(U),gray_card(U),gray_background(U),white(U),purple_200(U),purple_500(D),purple_700(U),teal_200(U),teal_700(D),black(U)],dimen[activity_horizontal_margin(D),activity_vertical_margin(D)],drawable[circle_pink_background(U),ic_album_cover(U),ic_chat(U),ic_dashboard_black_24dp(U),ic_heart(U),ic_home_black_24dp(U),ic_launcher_background(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R),ic_location(U),ic_menu(U),ic_notifications_black_24dp(U),ic_sunny(U),outfit_placeholder(U),pink_button_background(U)],id[container(D),nav_view(U),nav_host_fragment_activity_main(U),text_chat(D),text_dashboard(D),location_text(D),weather_card(D),temperature(D),weather_icon(D),weather_details(D),weather_detail_button(D),music_card(D),outfit_card(D),text_notifications(D),navigation_home(U),navigation_dashboard(U),navigation_notifications(U),navigation_chat(U),mobile_navigation(D)],layout[activity_main(U),fragment_chat(U),fragment_dashboard(U),fragment_home(U),fragment_notifications(U)],menu[bottom_nav_menu(U)],mipmap[ic_launcher(U),ic_launcher_round(U)],navigation[mobile_navigation(U)],string[app_name(U),title_home(U),title_dashboard(U),title_notifications(U),title_chat(U)],style[Theme_BLINKSKY(U),Theme_MaterialComponents_DayNight_NoActionBar(R),Theme_MaterialComponents_DayNight_DarkActionBar(R)],xml[data_extraction_rules(U),backup_rules(U)];2^3^4,14^5,15^3,18^3,1b^1c,1d^3,1e^6,21^7^8,22^5,36^0^9^2^3b^24^3e,39^a^1d^6^3^9^b^20^4^22^1e^14^15^18^21,3b^19^40^17^41^1f^42^16^43,3c^1a^1b,3d^1a^1b,3e^31^40^39^41^38^42^3a^43^37,44^45^3^a^b^5^9^46^c^e^11^f^1;;;"/>
        <location id="R.dimen.activity_horizontal_margin"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="3"
            column="12"
            startOffset="95"
            endLine="3"
            endColumn="45"
            endOffset="128"/>
        <location id="R.dimen.activity_vertical_margin"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="4"
            column="12"
            startOffset="153"
            endLine="4"
            endColumn="43"
            endOffset="184"/>
    </map>

</incidents>
