<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.6.0" type="incidents">

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.7.0"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="13"
            startOffset="100"
            endLine="6"
            endColumn="20"
            endOffset="107"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1">
        <fix-replace
            description="Change to 2.2.1"
            family="Update versions"
            oldString="2.1.4"
            replacement="2.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="8"
            column="20"
            startOffset="147"
            endLine="8"
            endColumn="27"
            endOffset="154"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.6.1 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="9"
            column="24"
            startOffset="178"
            endLine="9"
            endColumn="31"
            endOffset="185"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.6.1 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="10"
            column="25"
            startOffset="210"
            endLine="10"
            endColumn="32"
            endOffset="217"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="22"
            startOffset="239"
            endLine="11"
            endColumn="29"
            endOffset="246"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="12"
            column="16"
            startOffset="262"
            endLine="12"
            endColumn="23"
            endOffset="269"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.6.0 is available: 8.10.1. (There is also a newer version of 8.6.𝑥 available, if upgrading to 8.10.1 is difficult: 8.6.1)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.10.1"
                family="Update versions"
                oldString="8.6.0"
                replacement="8.10.1"
                priority="0"/>
            <fix-replace
                description="Change to 8.6.1"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.6.0"
                replacement="8.6.1"
                priority="0"/>
        </fix-alternatives>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="14"
            endOffset="24"/>
    </incident>

</incidents>
