<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.6.0" type="incidents">

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="18"
            column="13"
            startOffset="705"
            endLine="18"
            endColumn="45"
            endOffset="737"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (969 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sunny.xml"
            line="8"
            column="27"
            startOffset="256"
            endLine="8"
            endColumn="996"
            endOffset="1225"/>
    </incident>

    <incident
        id="FragmentTagUsage"
        severity="warning"
        message="Replace the &lt;fragment> tag with FragmentContainerView.">
        <fix-replace
            description="Replace with androidx.fragment.app.FragmentContainerView"
            oldString="fragment"
            replacement="androidx.fragment.app.FragmentContainerView"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="23"
            column="6"
            startOffset="992"
            endLine="23"
            endColumn="14"
            endOffset="1000"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="24"
            column="14"
            startOffset="846"
            endLine="24"
            endColumn="26"
            endOffset="858"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="31"
            column="18"
            startOffset="1115"
            endLine="31"
            endColumn="27"
            endOffset="1124"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;上海市&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="41"
            column="21"
            startOffset="1544"
            endLine="41"
            endColumn="39"
            endOffset="1562"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;logo&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="50"
            column="17"
            startOffset="1868"
            endLine="50"
            endColumn="36"
            endOffset="1887"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="73"
            column="18"
            startOffset="2709"
            endLine="73"
            endColumn="30"
            endOffset="2721"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;26°&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="84"
            column="25"
            startOffset="3214"
            endLine="84"
            endColumn="43"
            endOffset="3232"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="89"
            column="22"
            startOffset="3417"
            endLine="89"
            endColumn="31"
            endOffset="3426"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;晴朗 | 体感 28°&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="104"
            column="21"
            startOffset="4079"
            endLine="104"
            endColumn="47"
            endOffset="4105"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;湿度 68%&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="119"
            column="25"
            startOffset="4709"
            endLine="119"
            endColumn="46"
            endOffset="4730"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;风速 3km/h&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="127"
            column="25"
            startOffset="5060"
            endLine="127"
            endColumn="48"
            endOffset="5083"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;气压 1013hPa&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="135"
            column="25"
            startOffset="5413"
            endLine="135"
            endColumn="50"
            endOffset="5438"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;天气详情&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="146"
            column="21"
            startOffset="5899"
            endLine="146"
            endColumn="40"
            endOffset="5918"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="169"
            column="18"
            startOffset="6750"
            endLine="169"
            endColumn="30"
            endOffset="6762"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;正在播放&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="179"
            column="25"
            startOffset="7203"
            endLine="179"
            endColumn="44"
            endOffset="7222"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="184"
            column="22"
            startOffset="7407"
            endLine="184"
            endColumn="31"
            endOffset="7416"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="191"
            column="18"
            startOffset="7661"
            endLine="191"
            endColumn="30"
            endOffset="7673"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="203"
            column="26"
            startOffset="8153"
            endLine="203"
            endColumn="35"
            endOffset="8162"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="208"
            column="26"
            startOffset="8403"
            endLine="208"
            endColumn="35"
            endOffset="8412"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Pink Venom&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="225"
            column="29"
            startOffset="9170"
            endLine="225"
            endColumn="54"
            endOffset="9195"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;BLACKPINK&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="234"
            column="29"
            startOffset="9621"
            endLine="234"
            endColumn="53"
            endOffset="9645"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="241"
            column="18"
            startOffset="9882"
            endLine="241"
            endColumn="27"
            endOffset="9891"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;今日穿搭&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="276"
            column="25"
            startOffset="11275"
            endLine="276"
            endColumn="44"
            endOffset="11294"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;适合26°C&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="284"
            column="25"
            startOffset="11633"
            endLine="284"
            endColumn="46"
            endOffset="11654"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="290"
            column="18"
            startOffset="11847"
            endLine="290"
            endColumn="30"
            endOffset="11859"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="309"
            column="30"
            startOffset="12691"
            endLine="309"
            endColumn="39"
            endOffset="12700"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;清凉夏日风&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="321"
            column="29"
            startOffset="13344"
            endLine="321"
            endColumn="49"
            endOffset="13364"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="341"
            column="30"
            startOffset="14243"
            endLine="341"
            endColumn="39"
            endOffset="14252"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;甜美公主风&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="353"
            column="29"
            startOffset="14896"
            endLine="353"
            endColumn="49"
            endOffset="14916"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="372"
            column="30"
            startOffset="15737"
            endLine="372"
            endColumn="39"
            endOffset="15746"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;潮流&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="384"
            column="29"
            startOffset="16390"
            endLine="384"
            endColumn="46"
            endOffset="16407"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.7.0"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="13"
            startOffset="100"
            endLine="6"
            endColumn="20"
            endOffset="107"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1">
        <fix-replace
            description="Change to 2.2.1"
            family="Update versions"
            oldString="2.1.4"
            replacement="2.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="8"
            column="20"
            startOffset="147"
            endLine="8"
            endColumn="27"
            endOffset="154"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.6.1 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="9"
            column="24"
            startOffset="178"
            endLine="9"
            endColumn="31"
            endOffset="185"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.6.1 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="10"
            column="25"
            startOffset="210"
            endLine="10"
            endColumn="32"
            endOffset="217"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="22"
            startOffset="239"
            endLine="11"
            endColumn="29"
            endOffset="246"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="12"
            column="16"
            startOffset="262"
            endLine="12"
            endColumn="23"
            endOffset="269"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.6.0 is available: 8.10.1. (There is also a newer version of 8.6.𝑥 available, if upgrading to 8.10.1 is difficult: 8.6.1)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.10.1"
                family="Update versions"
                oldString="8.6.0"
                replacement="8.10.1"
                priority="0"/>
            <fix-replace
                description="Change to 8.6.1"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.6.0"
                replacement="8.6.1"
                priority="0"/>
        </fix-alternatives>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="14"
            endOffset="24"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/gray_background` with a theme that also paints a background (inferred theme is `@style/Theme.BLINKSKY`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="7"
            column="5"
            startOffset="302"
            endLine="7"
            endColumn="48"
            endOffset="345"/>
    </incident>

</incidents>
