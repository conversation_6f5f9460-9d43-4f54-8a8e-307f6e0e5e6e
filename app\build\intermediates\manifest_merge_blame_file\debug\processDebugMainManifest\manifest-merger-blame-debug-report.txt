1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.blinksky"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="27"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\8113180f4eede8d055c8c7243449a034\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.blinksky.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\8113180f4eede8d055c8c7243449a034\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\8113180f4eede8d055c8c7243449a034\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.blinksky.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\8113180f4eede8d055c8c7243449a034\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\8113180f4eede8d055c8c7243449a034\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:5:5-25:19
18        android:allowBackup="true"
18-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\8113180f4eede8d055c8c7243449a034\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:12:9-35
28        android:theme="@style/Theme.BLINKSKY" >
28-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:13:9-46
29        <activity
29-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:15:9-24:20
30            android:name="com.example.blinksky.MainActivity"
30-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:16:13-41
31            android:exported="true"
31-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:17:13-36
32            android:label="@string/app_name" >
32-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:18:13-45
33            <intent-filter>
33-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:19:13-23:29
34                <action android:name="android.intent.action.MAIN" />
34-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:20:17-69
34-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:20:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:22:17-77
36-->D:\BLINKSKY\app\src\main\AndroidManifest.xml:22:27-74
37            </intent-filter>
38        </activity>
39
40        <provider
40-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\95c5ec88c843ff7ffb9f7cb0bd6d501c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
41            android:name="androidx.startup.InitializationProvider"
41-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\95c5ec88c843ff7ffb9f7cb0bd6d501c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
42            android:authorities="com.example.blinksky.androidx-startup"
42-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\95c5ec88c843ff7ffb9f7cb0bd6d501c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
43            android:exported="false" >
43-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\95c5ec88c843ff7ffb9f7cb0bd6d501c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
44            <meta-data
44-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\95c5ec88c843ff7ffb9f7cb0bd6d501c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
45                android:name="androidx.emoji2.text.EmojiCompatInitializer"
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\95c5ec88c843ff7ffb9f7cb0bd6d501c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
46                android:value="androidx.startup" />
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\95c5ec88c843ff7ffb9f7cb0bd6d501c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
47            <meta-data
47-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b3d3c7e31a7c77f7d06193606c4ba302\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
48                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
48-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b3d3c7e31a7c77f7d06193606c4ba302\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
49                android:value="androidx.startup" />
49-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b3d3c7e31a7c77f7d06193606c4ba302\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
50            <meta-data
50-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
51-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
52                android:value="androidx.startup" />
52-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
53        </provider>
54
55        <uses-library
55-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab33095dbae0dacf9043f3cf95883f86\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
56            android:name="androidx.window.extensions"
56-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab33095dbae0dacf9043f3cf95883f86\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
57            android:required="false" />
57-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab33095dbae0dacf9043f3cf95883f86\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
58        <uses-library
58-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab33095dbae0dacf9043f3cf95883f86\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
59            android:name="androidx.window.sidecar"
59-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab33095dbae0dacf9043f3cf95883f86\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
60            android:required="false" />
60-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab33095dbae0dacf9043f3cf95883f86\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
61
62        <receiver
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
63            android:name="androidx.profileinstaller.ProfileInstallReceiver"
63-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
64            android:directBootAware="false"
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
65            android:enabled="true"
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
66            android:exported="true"
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
67            android:permission="android.permission.DUMP" >
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
68            <intent-filter>
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
69                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
70            </intent-filter>
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
72                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
73            </intent-filter>
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
75                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
78                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\cee82141340cfe36a154d4d5afcd5a3b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
79            </intent-filter>
80        </receiver>
81    </application>
82
83</manifest>
