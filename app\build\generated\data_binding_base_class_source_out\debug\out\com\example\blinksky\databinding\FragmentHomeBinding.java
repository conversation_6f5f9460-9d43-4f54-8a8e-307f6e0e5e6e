// Generated by view binder compiler. Do not edit!
package com.example.blinksky.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.blinksky.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentHomeBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final TextView locationText;

  @NonNull
  public final CardView musicCard;

  @NonNull
  public final CardView outfitCard;

  @NonNull
  public final TextView temperature;

  @NonNull
  public final CardView weatherCard;

  @NonNull
  public final Button weatherDetailButton;

  @NonNull
  public final TextView weatherDetails;

  @NonNull
  public final ImageView weatherIcon;

  private FragmentHomeBinding(@NonNull ScrollView rootView, @NonNull TextView locationText,
      @NonNull CardView musicCard, @NonNull CardView outfitCard, @NonNull TextView temperature,
      @NonNull CardView weatherCard, @NonNull Button weatherDetailButton,
      @NonNull TextView weatherDetails, @NonNull ImageView weatherIcon) {
    this.rootView = rootView;
    this.locationText = locationText;
    this.musicCard = musicCard;
    this.outfitCard = outfitCard;
    this.temperature = temperature;
    this.weatherCard = weatherCard;
    this.weatherDetailButton = weatherDetailButton;
    this.weatherDetails = weatherDetails;
    this.weatherIcon = weatherIcon;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_home, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHomeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.location_text;
      TextView locationText = ViewBindings.findChildViewById(rootView, id);
      if (locationText == null) {
        break missingId;
      }

      id = R.id.music_card;
      CardView musicCard = ViewBindings.findChildViewById(rootView, id);
      if (musicCard == null) {
        break missingId;
      }

      id = R.id.outfit_card;
      CardView outfitCard = ViewBindings.findChildViewById(rootView, id);
      if (outfitCard == null) {
        break missingId;
      }

      id = R.id.temperature;
      TextView temperature = ViewBindings.findChildViewById(rootView, id);
      if (temperature == null) {
        break missingId;
      }

      id = R.id.weather_card;
      CardView weatherCard = ViewBindings.findChildViewById(rootView, id);
      if (weatherCard == null) {
        break missingId;
      }

      id = R.id.weather_detail_button;
      Button weatherDetailButton = ViewBindings.findChildViewById(rootView, id);
      if (weatherDetailButton == null) {
        break missingId;
      }

      id = R.id.weather_details;
      TextView weatherDetails = ViewBindings.findChildViewById(rootView, id);
      if (weatherDetails == null) {
        break missingId;
      }

      id = R.id.weather_icon;
      ImageView weatherIcon = ViewBindings.findChildViewById(rootView, id);
      if (weatherIcon == null) {
        break missingId;
      }

      return new FragmentHomeBinding((ScrollView) rootView, locationText, musicCard, outfitCard,
          temperature, weatherCard, weatherDetailButton, weatherDetails, weatherIcon);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
