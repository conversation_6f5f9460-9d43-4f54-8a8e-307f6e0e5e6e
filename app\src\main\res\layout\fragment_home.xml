<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5"
    tools:context=".ui.home.HomeFragment">

    <!-- 顶部地理位置 -->
    <TextView
        android:id="@+id/location_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:text="上海市"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 天气卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/weather_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/location_text">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/temperature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="26°"
                    android:textSize="48sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/weather_icon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="16dp"
                    android:src="@drawable/ic_sunny" />
            </LinearLayout>

            <TextView
                android:id="@+id/weather_details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="晴朗 | 体感 28°" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="湿度 68%" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="风速 3km/h" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="气压 1013hPa" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 正在播放卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/music_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/weather_card">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="正在播放" />

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="16dp"
                android:src="@drawable/album_cover" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Pink Venom"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="BLACKPINK" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 今日穿搭卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/outfit_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/music_card">

        <LinearLayout
            android:layout_width="match_parent"
            android_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="今日穿搭"
                android:textStyle="bold"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="适合 26°" />

            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <androidx.cardview.widget.CardView
                        android:layout_width="120dp"
                        android:layout_height="160dp"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="8dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:scaleType="centerCrop"
                                android:src="@drawable/outfit1" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:padding="8dp"
                                android:text="清凉夏日风" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:layout_width="120dp"
                        android:layout_height="160dp"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="8dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:scaleType="centerCrop"
                                android:src="@drawable/outfit2" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:padding="8dp"
                                android:text="甜美公主风" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:layout_width="120dp"
                        android:layout_height="160dp"
                        app:cardCornerRadius="8dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:scaleType="centerCrop"
                                android:src="@drawable/outfit3" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:padding="8dp"
                                android:text="潮流" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>
            </HorizontalScrollView>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>