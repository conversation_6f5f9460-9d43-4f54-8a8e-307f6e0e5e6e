<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_demo"
    tools:context=".ui.home.HomeFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 顶部区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <!-- 左侧位置信息 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginEnd="4dp"
                    android:src="@drawable/ic_location" />

                <TextView
                    android:id="@+id/location_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="上海市"
                    android:textColor="@color/text_white"
                    android:textSize="16sp" />
            </LinearLayout>

            <!-- 右侧logo -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="logo"
                android:textColor="@color/pink_primary"
                android:textSize="18sp"
                android:textStyle="italic" />
        </LinearLayout>

        <!-- 天气卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/weather_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/black"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 温度和天气图标 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/temperature"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="26°"
                        android:textColor="@color/text_white"
                        android:textSize="64sp"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/weather_icon"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:background="@color/white"
                        android:padding="16dp"
                        android:src="@drawable/ic_sunny" />
                </LinearLayout>

                <!-- 天气详情 -->
                <TextView
                    android:id="@+id/weather_details"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="晴朗 | 体感 28°"
                    android:textColor="@color/text_gray"
                    android:textSize="16sp" />

                <!-- 天气参数 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="湿度 68%"
                        android:textColor="@color/text_gray"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="风速 3km/h"
                        android:textColor="@color/text_gray"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="气压 1013hPa"
                        android:textColor="@color/text_gray"
                        android:textSize="14sp" />
                </LinearLayout>

                <!-- 天气详情按钮 -->
                <Button
                    android:id="@+id/weather_detail_button"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/pink_button_background"
                    android:text="天气详情"
                    android:textColor="@color/pink_primary"
                    android:textSize="16sp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 正在播放卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/music_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/black"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 标题栏 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="正在播放"
                        android:textColor="@color/text_white"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_menu" />
                </LinearLayout>

                <!-- 音乐信息 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <!-- 专辑封面 -->
                    <FrameLayout
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:layout_marginEnd="16dp">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/circle_pink_background" />

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_album_cover" />
                    </FrameLayout>

                    <!-- 歌曲信息 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Pink Venom"
                            android:textColor="@color/text_white"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="BLACKPINK"
                            android:textColor="@color/text_gray"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>

                <!-- 爱心图标 -->
                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_heart" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 今日穿搭卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/outfit_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/black"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 标题栏 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="今日穿搭"
                        android:textColor="@color/text_white"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="适合26°C"
                        android:textColor="@color/text_gray"
                        android:textSize="14sp" />
                </LinearLayout>

                <!-- 穿搭推荐 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- 清凉夏日风 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:orientation="vertical">

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="160dp"
                            app:cardBackgroundColor="@color/black"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="0dp">

                            <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/outfit_placeholder" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:gravity="center"
                            android:text="清凉夏日风"
                            android:textColor="@color/text_white"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <!-- 甜美公主风 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="4dp"
                        android:orientation="vertical">

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="160dp"
                            app:cardBackgroundColor="@color/black"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="0dp">

                            <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/outfit_placeholder" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:gravity="center"
                            android:text="甜美公主风"
                            android:textColor="@color/text_white"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <!-- 潮流 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:orientation="vertical">

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="160dp"
                            app:cardBackgroundColor="@color/black"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="0dp">

                            <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/outfit_placeholder" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:gravity="center"
                            android:text="潮流"
                            android:textColor="@color/text_white"
                            android:textSize="12sp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>
</ScrollView>