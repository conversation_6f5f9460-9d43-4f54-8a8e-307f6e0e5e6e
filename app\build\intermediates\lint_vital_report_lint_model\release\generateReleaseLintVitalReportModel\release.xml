<variant
    name="release"
    package="com.example.blinksky"
    minSdkVersion="27"
    targetSdkVersion="34"
    mergedManifest="build\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.6.0;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      viewBinding="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\release\compileReleaseJavaWithJavac\classes;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.example.blinksky"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out;build\generated\data_binding_base_class_source_out\release\out"
      generatedResourceFolders="build\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-4\8d5b08e7317ec6fc2e59e5cd8493c2bd\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
