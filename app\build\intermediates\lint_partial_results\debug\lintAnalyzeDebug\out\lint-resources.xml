http://schemas.android.com/apk/res-auto;;${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/color/bottom_nav_color.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/outfit_placeholder.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_heart.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/pink_button_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notifications_black_24dp.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chat.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_home_black_24dp.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_album_cover.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/circle_pink_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_dashboard_black_24dp.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sunny.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_dashboard.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_nav_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/navigation/mobile_navigation.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_chat.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_notifications.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher_round.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:purple_200,0,V400020037,2e00020061,;"#FFBB86FC";pink_primary,0,V4000b018a,30000b01b6,;"#FFE91E63";gray_card,0,V4000f024c,2d000f0275,;"#FF757575";pink_light,0,V4000c01bb,2e000c01e5,;"#FFF8BBD9";black,0,V40007011e,**********,;"#FF000000";teal_200,0,V4000500c4,2c000500ec,;"#FF03DAC5";bottom_nav_color,1,F;gray_light,0,V40010027a,2e001002a4,;"#FFE0E0E0";white,0,V400080148,290008016d,;"#FFFFFFFF";gray_background,0,V4000d01ea,33000d0219,;"#FF9E9E9E";teal_700,0,V4000600f1,2c00060119,;"#FF018786";text_white,0,V4001102a9,2e001102d3,;"#FFFFFFFF";text_gray,0,V4001202d8,2d00120301,;"#FFBDBDBD";purple_700,0,V400040095,2e000400bf,;"#FF3700B3";purple_500,0,V400030066,2e00030090,;"#FF6200EE";gray_dark,0,V4000e021e,2d000e0247,;"#FF616161";+dimen:activity_vertical_margin,2,V400030092,37000300c5,;"16dp";activity_horizontal_margin,2,V400020058,390002008d,;"16dp";+drawable:outfit_placeholder,3,F;ic_heart,4,F;pink_button_background,5,F;ic_menu,6,F;ic_location,7,F;ic_notifications_black_24dp,8,F;ic_launcher_foreground,9,F;ic_launcher_background,10,F;ic_chat,11,F;ic_home_black_24dp,12,F;ic_album_cover,13,F;circle_pink_background,14,F;ic_dashboard_black_24dp,15,F;ic_sunny,16,F;+id:container,17,F;text_dashboard,18,F;navigation_dashboard,19,F;navigation_dashboard,20,F;navigation_notifications,19,F;navigation_notifications,20,F;nav_view,17,F;text_chat,21,F;nav_host_fragment_activity_main,17,F;weather_icon,22,F;weather_details,22,F;music_card,22,F;outfit_card,22,F;navigation_home,19,F;navigation_home,20,F;navigation_home,20,F;temperature,22,F;text_notifications,23,F;navigation_chat,19,F;navigation_chat,20,F;mobile_navigation,20,F;location_text,22,F;weather_card,22,F;weather_detail_button,22,F;+layout:fragment_chat,21,F;fragment_dashboard,18,F;activity_main,17,F;fragment_home,22,F;fragment_notifications,23,F;+menu:bottom_nav_menu,19,F;+mipmap:ic_launcher_round,24,F;ic_launcher_round,25,F;ic_launcher_round,26,F;ic_launcher_round,27,F;ic_launcher_round,28,F;ic_launcher_round,29,F;ic_launcher,30,F;ic_launcher,31,F;ic_launcher,32,F;ic_launcher,33,F;ic_launcher,34,F;ic_launcher,35,F;+navigation:mobile_navigation,20,F;+string:title_notifications,36,V400040097,32000400c5,;"娱乐";app_name,36,V400010010,2d00010039,;"BLINKSKY";title_home,36,V40002003e,2900020063,;"首页";title_dashboard,36,V400030068,2e00030092,;"日记";title_chat,36,V4000500ca,29000500ef,;"聊天";+style:Theme.BLINKSKY,37,V400020064,c001103c7,;DTheme.MaterialComponents.DayNight.NoActionBar,colorPrimary:@color/pink_primary,colorPrimaryVariant:@color/gray_background,colorOnPrimary:@color/white,colorSecondary:@color/pink_primary,colorSecondaryVariant:@color/pink_light,colorOnSecondary:@color/white,colorSurface:@color/gray_card,android\:statusBarColor:@color/gray_background,android\:windowLightStatusBar:false,;Theme.BLINKSKY,38,V400020064,c000e031d,;DTheme.MaterialComponents.DayNight.DarkActionBar,colorPrimary:@color/purple_200,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/black,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_200,colorOnSecondary:@color/black,android\:statusBarColor:?attr/colorPrimaryVariant,;+xml:data_extraction_rules,39,F;backup_rules,40,F;